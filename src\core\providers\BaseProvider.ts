import { ProviderClient, ChatMessage, BaseTool } from '../types';

export interface ProviderConfig {
  apiKey: string;
  baseUrl?: string;
  defaultModel: string;
  maxTokens?: number;
  temperature?: number;
  systemPrompt?: string;
}

export interface ProviderOptions {
  model?: string;
  maxTokens?: number;
  temperature?: number;
  stream?: boolean;
  tools?: BaseTool[];
}

export interface ProviderResponse {
  content: string;
  model: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  toolCalls?: Array<{
    id: string;
    name: string;
    parameters: Record<string, any>;
  }>;
  finishReason?: string;
}

export abstract class BaseProvider implements ProviderClient {
  protected config: ProviderConfig;
  public abstract name: string;

  constructor(config: ProviderConfig) {
    this.config = config;
  }

  public isConfigured(): boolean {
    return !!(this.config.apiKey && this.config.apiKey.trim().length > 0);
  }

  public abstract sendMessage(
    messages: ChatMessage[],
    tools?: BaseTool[],
    options?: ProviderOptions
  ): Promise<ChatMessage>;

  public abstract streamMessage?(
    messages: ChatMessage[],
    tools?: BaseTool[],
    options?: ProviderOptions
  ): AsyncGenerator<string, ChatMessage>;

  protected formatMessages(messages: ChatMessage[]): any[] | { systemMessage?: string; formattedMessages: any[] } {
    return messages.map(msg => ({
      role: msg.role,
      content: msg.content,
    }));
  }

  protected formatTools(tools?: BaseTool[]): any[] {
    if (!tools || tools.length === 0) {
      return [];
    }

    return tools.map(tool => ({
      type: 'function',
      function: {
        name: tool.name,
        description: tool.description,
        parameters: tool.parameters,
      },
    }));
  }

  protected createChatMessage(
    content: string,
    role: 'assistant' = 'assistant',
    metadata?: any
  ): ChatMessage {
    // Extract toolCalls from metadata to put at top level
    const { toolCalls, ...restMetadata } = metadata || {};

    return {
      id: this.generateId(),
      role,
      content,
      timestamp: new Date(),
      ...(toolCalls && { toolCalls }),
      ...(Object.keys(restMetadata).length > 0 && { metadata: restMetadata }),
    };
  }

  protected generateId(): string {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15);
  }

  protected handleError(error: any, context: string): Error {
    console.error(`${this.name} Provider Error (${context}):`, error);
    
    if (error.response) {
      const status = error.response.status;
      const message = error.response.data?.error?.message || error.message;
      
      switch (status) {
        case 401:
          return new Error(`Authentication failed: Invalid API key for ${this.name}`);
        case 403:
          return new Error(`Access forbidden: Check your ${this.name} API permissions`);
        case 429:
          return new Error(`Rate limit exceeded for ${this.name}. Please try again later.`);
        case 500:
        case 502:
        case 503:
          return new Error(`${this.name} service is temporarily unavailable`);
        default:
          return new Error(`${this.name} API error (${status}): ${message}`);
      }
    }
    
    if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
      return new Error(`Cannot connect to ${this.name} API. Check your internet connection.`);
    }
    
    return new Error(`${this.name} provider error: ${error.message || error}`);
  }

  protected validateConfig(): void {
    if (!this.config.apiKey) {
      throw new Error(`API key is required for ${this.name} provider`);
    }
    
    if (!this.config.defaultModel) {
      throw new Error(`Default model is required for ${this.name} provider`);
    }
  }

  protected getRequestOptions(options?: ProviderOptions) {
    return {
      model: options?.model || this.config.defaultModel,
      maxTokens: options?.maxTokens || this.config.maxTokens || 4000,
      temperature: options?.temperature ?? this.config.temperature ?? 0.7,
      stream: options?.stream || false,
    };
  }

  public updateConfig(updates: Partial<ProviderConfig>): void {
    this.config = { ...this.config, ...updates };
  }

  public getConfig(): ProviderConfig {
    return { ...this.config };
  }

  public async testConnection(): Promise<boolean> {
    try {
      const testMessage: ChatMessage = {
        id: 'test',
        role: 'user',
        content: 'Hello',
        timestamp: new Date(),
      };

      await this.sendMessage([testMessage], [], { maxTokens: 10 });
      return true;
    } catch (error) {
      console.error(`Connection test failed for ${this.name}:`, error);
      return false;
    }
  }

  public async getAvailableModels(): Promise<string[]> {
    // Default implementation returns empty array
    // Subclasses can override to fetch from API
    return [];
  }
}
