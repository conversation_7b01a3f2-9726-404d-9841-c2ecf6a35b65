import { CLIState, CLIConfig, CLIComponent } from '../types';
import { ConfigManager } from '../../core/config/ConfigManager';
import { AIEngine, ToolConfirmationHandler } from '../../core/engine';
import { createDefaultToolRegistry } from '../../core/tools';
import { AuthManager } from '../components/auth/AuthManager';
import { TerminalManager } from '../components/terminal/TerminalManager';
import { ConfigComponent } from '../components/config/ConfigComponent';
import { HistoryComponent } from '../components/history/HistoryComponent';
import { ErrorHandlingMiddleware, ProgressCallback } from '../../core/error-handling/ErrorHandlingMiddleware';
import { createErrorHandlingMiddleware } from '../../core/error-handling';
import { CLIProgressIndicator } from '../components/common/ProgressIndicator';

export class CLIService {
  private state: CLIState;
  private config: CLIConfig;
  private configManager: ConfigManager;
  private aiEngine: AIEngine;
  private components: Map<string, CLIComponent> = new Map();
  private toolConfirmationHandler: ToolConfirmationHandler;
  private errorHandler: ErrorHandlingMiddleware;
  private progressIndicator: CLIProgressIndicator;

  constructor(configManager: ConfigManager) {
    this.configManager = configManager;
    
    // Initialize default state
    this.state = {
      currentView: 'auth',
      isAuthenticated: false,
      currentProvider: '',
      currentModel: '',
    };

    // Initialize default config
    this.config = {
      theme: {
        primary: 'cyan',
        secondary: 'blue',
        success: 'green',
        error: 'red',
        warning: 'yellow',
        info: 'blue',
        muted: 'gray',
        accent: 'magenta',
        background: 'black',
        text: 'white',
      },
      terminal: {
        prompt: '🤖 AI CLI',
        maxHistorySize: 1000,
        autoSave: true,
        showTimestamps: true,
        showTokenCounts: true,
        streamResponses: true,
      },
      ui: {
        animations: true,
        sounds: false,
        compactMode: false,
        showBanner: true,
      },
      showTimestamps: true,
      showTokenCounts: true,
    };

    // Initialize error handling middleware with CLI-specific configuration
    this.errorHandler = createErrorHandlingMiddleware({
      showRetryProgress: true,
      allowRetryCancel: true,
      enableErrorLogging: true,
      enableMetrics: true
    });

    // Initialize progress indicator
    this.progressIndicator = new CLIProgressIndicator(this.config.theme);

    // Initialize AI engine with tool registry
    const toolRegistry = createDefaultToolRegistry();
    this.toolConfirmationHandler = {
      confirmExecution: async (toolName: string, parameters: Record<string, any>): Promise<boolean> => {
        const terminalManager = this.components.get('terminal') as TerminalManager;
        if (terminalManager) {
          return await terminalManager.confirmToolExecution(toolName, parameters);
        }
        return false;
      },
      confirmToolExecution: async (toolName: string, parameters: Record<string, any>): Promise<boolean> => {
        const terminalManager = this.components.get('terminal') as TerminalManager;
        if (terminalManager) {
          return await terminalManager.confirmToolExecution(toolName, parameters);
        }
        return false;
      }
    };

    this.aiEngine = new AIEngine(configManager, toolRegistry, this.toolConfirmationHandler);

    this.initializeComponents();
  }

  private initializeComponents(): void {
    // Initialize all CLI components
    this.components.set('auth', new AuthManager(this.state, this.config, this.configManager));
    this.components.set('terminal', new TerminalManager(this.state, this.config, this.configManager, this.aiEngine));
    this.components.set('config', new ConfigComponent(this.state, this.config, this.configManager));
    this.components.set('history', new HistoryComponent(this.state, this.config, this.configManager));
  }

  public async start(): Promise<void> {
    // Check if any providers are configured
    const configuredProviders = this.configManager.getConfiguredProviders();
    
    if (configuredProviders.length === 0) {
      // No providers configured, start with auth
      this.state.currentView = 'auth';
      this.state.isAuthenticated = false;
    } else {
      // Providers configured, check if we can authenticate
      const defaultProvider = this.configManager.getDefaultProvider();
      if (defaultProvider && this.configManager.isProviderConfigured(defaultProvider)) {
        this.state.currentProvider = defaultProvider;
        this.state.currentModel = this.configManager.getProviderConfig(defaultProvider).defaultModel;
        this.state.isAuthenticated = true;
        this.state.currentView = 'terminal';
      } else {
        this.state.currentView = 'auth';
        this.state.isAuthenticated = false;
      }
    }

    await this.renderCurrentView();
  }

  public async renderCurrentView(): Promise<void> {
    const component = this.components.get(this.state.currentView);
    if (component) {
      await component.render();
    } else {
      throw new Error(`Unknown view: ${this.state.currentView}`);
    }
  }

  public async switchView(view: CLIState['currentView']): Promise<void> {
    // Cleanup current component
    const currentComponent = this.components.get(this.state.currentView);
    if (currentComponent && currentComponent.cleanup) {
      await currentComponent.cleanup();
    }

    // Update state
    this.state.currentView = view;

    // Render new view
    await this.renderCurrentView();
  }

  public async handleInput(input: string): Promise<void> {
    const component = this.components.get(this.state.currentView);
    if (component && component.handleInput) {
      await component.handleInput(input);
    }
  }

  public getState(): CLIState {
    return { ...this.state };
  }

  public updateState(updates: Partial<CLIState>): void {
    Object.assign(this.state, updates);
  }

  public getConfig(): CLIConfig {
    return { ...this.config };
  }

  public updateConfig(updates: Partial<CLIConfig>): void {
    Object.assign(this.config, updates);
  }

  public getAIEngine(): AIEngine {
    return this.aiEngine;
  }

  public getConfigManager(): ConfigManager {
    return this.configManager;
  }

  public async shutdown(): Promise<void> {
    // Cleanup all components
    for (const [name, component] of this.components) {
      if (component.cleanup) {
        try {
          await component.cleanup();
        } catch (error) {
          console.error(`Error cleaning up component ${name}:`, error);
        }
      }
    }

    // Clear components
    this.components.clear();
  }

  // Navigation helpers
  public async goToAuth(): Promise<void> {
    await this.switchView('auth');
  }

  public async goToTerminal(): Promise<void> {
    if (!this.state.isAuthenticated) {
      throw new Error('Must be authenticated to access terminal');
    }
    await this.switchView('terminal');
  }

  public async goToConfig(): Promise<void> {
    await this.switchView('config');
  }

  public async goToHistory(): Promise<void> {
    await this.switchView('history');
  }

  // Authentication helpers
  public async authenticate(provider: string, model: string): Promise<void> {
    this.state.isAuthenticated = true;
    this.state.currentProvider = provider;
    this.state.currentModel = model;
  }

  public async logout(): Promise<void> {
    this.state.isAuthenticated = false;
    this.state.currentProvider = '';
    this.state.currentModel = '';
    this.state.conversationId = undefined;
    await this.switchView('auth');
  }

  // Provider management
  public async switchProvider(provider: string): Promise<void> {
    if (!this.configManager.isProviderConfigured(provider)) {
      throw new Error(`Provider ${provider} is not configured`);
    }

    const config = this.configManager.getProviderConfig(provider);
    this.state.currentProvider = provider;
    this.state.currentModel = config.defaultModel;
    
    await this.configManager.setDefaultProvider(provider);
  }

  public async switchModel(model: string): Promise<void> {
    this.state.currentModel = model;
  }

  // Conversation management
  public async startNewConversation(): Promise<void> {
    this.state.conversationId = undefined;
    
    // Reset terminal if we're in terminal view
    if (this.state.currentView === 'terminal') {
      const terminalManager = this.components.get('terminal') as TerminalManager;
      if (terminalManager) {
        await terminalManager.startNewConversation();
      }
    }
  }

  public async loadConversation(conversationId: string): Promise<void> {
    this.state.conversationId = conversationId;
    
    // Load conversation in terminal if we're in terminal view
    if (this.state.currentView === 'terminal') {
      const terminalManager = this.components.get('terminal') as TerminalManager;
      if (terminalManager) {
        await terminalManager.loadConversation(conversationId);
      }
    }
  }

  // Error handling with retry support
  public async handleError(error: Error): Promise<void> {
    console.error('CLI Service Error:', error);

    // Show error to user through current component
    const component = this.components.get(this.state.currentView);
    if (component && 'showError' in component) {
      await (component as any).showError(error.message);
    } else {
      console.error('Error:', error.message);
    }
  }

  /**
   * Execute an operation with comprehensive error handling and user feedback
   */
  public async executeWithErrorHandling<T>(
    operation: () => Promise<T>,
    operationName: string,
    options?: {
      showProgress?: boolean;
      allowCancel?: boolean;
      userFriendlyName?: string;
    }
  ): Promise<T> {
    const { showProgress = true, allowCancel = true, userFriendlyName } = options || {};

    // Create progress callback for user feedback
    const progressCallback: ProgressCallback = {
      onRetryAttempt: (attempt: number, maxAttempts: number, delay: number, error: Error) => {
        if (showProgress) {
          const friendlyName = userFriendlyName || operationName;
          this.progressIndicator.update(
            `${friendlyName} failed (attempt ${attempt}/${maxAttempts}). Retrying in ${Math.round(delay / 1000)}s...`
          );
        }
      },
      onCircuitBreakerOpen: (serviceName: string) => {
        if (showProgress) {
          this.progressIndicator.fail(`Service ${serviceName} is temporarily unavailable. Please try again later.`);
        }
      },
      onCircuitBreakerHalfOpen: (serviceName: string) => {
        if (showProgress) {
          this.progressIndicator.update(`Testing ${serviceName} service recovery...`);
        }
      },
      onCircuitBreakerClosed: (serviceName: string) => {
        if (showProgress) {
          this.progressIndicator.succeed(`${serviceName} service recovered successfully.`);
        }
      }
    };

    if (showProgress) {
      const friendlyName = userFriendlyName || operationName;
      this.progressIndicator.start(`Executing ${friendlyName}...`);
    }

    try {
      const result = await this.errorHandler.executeWithErrorHandling(
        operation,
        operationName,
        {
          progressCallback: allowCancel ? progressCallback : undefined,
          metadata: {
            userInitiated: true,
            component: this.state.currentView
          }
        }
      );

      if (showProgress) {
        const friendlyName = userFriendlyName || operationName;
        this.progressIndicator.succeed(`${friendlyName} completed successfully.`);
      }

      return result;
    } catch (error) {
      if (showProgress) {
        const friendlyName = userFriendlyName || operationName;
        this.progressIndicator.fail(`${friendlyName} failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }

      // Handle the error through the existing error handling system
      await this.handleError(error instanceof Error ? error : new Error(String(error)));
      throw error;
    }
  }

  /**
   * Cancel an ongoing operation
   */
  public cancelOperation(operationId: string): void {
    this.errorHandler.cancelOperation(operationId);
    this.progressIndicator.fail('Operation cancelled by user.');
  }

  /**
   * Get error handling metrics for debugging
   */
  public getErrorMetrics(): any {
    return this.errorHandler.getMetrics();
  }

  // Utility methods
  public isAuthenticated(): boolean {
    return this.state.isAuthenticated;
  }

  public getCurrentProvider(): string {
    return this.state.currentProvider;
  }

  public getCurrentModel(): string {
    return this.state.currentModel;
  }

  public getCurrentView(): CLIState['currentView'] {
    return this.state.currentView;
  }
}
