// Core types for the AI CLI Terminal system

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system' | 'tool';
  content: string;
  timestamp: Date;
  metadata?: {
    provider?: string;
    model?: string;
    tokens?: number;
    duration?: number;
    toolCallId?: string;
    toolCalls?: ToolCall[];
    usage?: TokenUsage;
    finishReason?: string;
  };
  toolCalls?: ToolCall[];
}

export interface ToolCall {
  id: string;
  name: string;
  parameters: Record<string, any>;
  result?: any;
  error?: string;
}

export interface TokenUsage {
  promptTokens: number;
  completionTokens: number;
  totalTokens: number;
}

export interface BaseTool {
  name: string;
  description: string;
  parameters: {
    type: 'object';
    properties: Record<string, any>;
    required?: string[];
  };
  requiresConfirmation: boolean;
}

export interface ToolResult {
  success: boolean;
  content: string;
  displayContent?: string;
  error?: string;
  metadata?: Record<string, any>;
}

export interface ProviderClient {
  name: string;
  isConfigured(): boolean;
  sendMessage(
    messages: ChatMessage[],
    tools?: BaseTool[],
    options?: any
  ): Promise<ChatMessage>;
  streamMessage?(
    messages: ChatMessage[],
    tools?: BaseTool[],
    options?: any
  ): AsyncGenerator<string, ChatMessage>;
  testConnection(): Promise<boolean>;
  getAvailableModels(): Promise<string[]>;
}

// Configuration types
export interface AppConfig {
  defaultProvider: string;
  configDir: string;
  providers: Record<string, ProviderConfiguration>;
  tools: ToolConfiguration;
  security: SecurityConfiguration;
  ui: UIConfiguration;
  systemPrompts: SystemPromptConfiguration;
  errorHandling: ErrorHandlingConfiguration;
  preferences: {
    requireToolConfirmation: boolean;
  };
}

export interface ProviderConfiguration {
  apiKey: string;
  baseUrl?: string;
  defaultModel: string;
  maxTokens?: number;
  temperature?: number;
  systemPrompt?: string;
  enabled: boolean;
  advanced?: {
    maxTokens?: number;
    temperature?: number;
    systemPrompt?: string;
  };
}

// System Prompt Management Types
export interface SystemPrompt {
  id: string;
  name: string;
  description: string;
  content: string;
  category?: string;
  tags?: string[];
  isDefault?: boolean;
  isActive?: boolean;
  createdAt: Date;
  updatedAt: Date;
  metadata?: {
    author?: string;
    version?: string;
    [key: string]: any;
  };
}

export interface SystemPromptConfiguration {
  activePromptId?: string;
  prompts: Record<string, SystemPrompt>;
  categories: string[];
  defaultPromptId?: string;
}

export interface ToolConfiguration {
  enableShellTools: boolean;
  enableWebTools: boolean;
  enableFileTools: boolean;
  enableMemoryTools: boolean;
  requireConfirmation: boolean;
  sandboxMode: boolean;
  maxFileSize: string;
  allowedCommands: string[];
  blockedCommands: string[];
}

export interface SecurityConfiguration {
  requireToolConfirmation: boolean;
  sandboxMode: boolean;
  maxFileSize: number;
  allowedPaths: string[];
  blockedPaths: string[];
  allowedDomains: string[];
  blockedDomains: string[];
}

export interface UIConfiguration {
  theme: string;
  showTimestamps: boolean;
  showTokenCounts: boolean;
  maxHistorySize: number;
  autoSave: boolean;
  streamResponses: boolean;
}

// Error Handling and Retry Configuration
export interface RetryConfiguration {
  /** Maximum number of retry attempts (default: 3) */
  maxAttempts: number;

  /** Base delay in milliseconds (default: 1000) */
  baseDelay: number;

  /** Backoff multiplier (default: 2) */
  backoffMultiplier: number;

  /** Maximum delay cap in milliseconds (default: 60000) */
  maxDelay: number;

  /** Enable jitter to prevent thundering herd (default: true) */
  enableJitter: boolean;

  /** Jitter type: 'full' | 'decorrelated' (default: 'full') */
  jitterType: 'full' | 'decorrelated';

  /** Timeout for each individual attempt in milliseconds */
  attemptTimeout?: number;
}

export interface CircuitBreakerConfiguration {
  /** Number of consecutive failures to trigger OPEN state (default: 5) */
  failureThreshold: number;

  /** Time in milliseconds to wait before transitioning to HALF_OPEN (default: 30000) */
  recoveryTimeout: number;

  /** Number of successful calls in HALF_OPEN to transition to CLOSED (default: 2) */
  successThreshold: number;

  /** Time window in milliseconds for failure counting (default: 60000) */
  monitoringWindow: number;

  /** Enable circuit breaker functionality (default: true) */
  enabled: boolean;
}

export interface ErrorHandlingConfiguration {
  /** Global retry policy settings */
  retry: RetryConfiguration;

  /** Circuit breaker settings */
  circuitBreaker: CircuitBreakerConfiguration;

  /** Enable comprehensive error logging (default: true) */
  enableErrorLogging: boolean;

  /** Enable metrics collection (default: true) */
  enableMetrics: boolean;

  /** Show retry progress to user (default: true) */
  showRetryProgress: boolean;

  /** Allow user to cancel retry sequences (default: true) */
  allowRetryCancel: boolean;

  /** Specific retry policies for different operation types */
  operationPolicies: {
    toolExecution: Partial<RetryConfiguration>;
    providerCalls: Partial<RetryConfiguration>;
    fileOperations: Partial<RetryConfiguration>;
    networkRequests: Partial<RetryConfiguration>;
  };
}

// Supported providers
export const SUPPORTED_PROVIDERS: Record<string, {
  name: string;
  displayName: string;
  models: string[];
  description: string;
}> = {
  openai: {
    name: 'openai',
    displayName: 'OpenAI',
    models: ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo'],
    description: 'OpenAI GPT models'
  },
  anthropic: {
    name: 'anthropic',
    displayName: 'Anthropic',
    models: ['claude-3-sonnet-20240229', 'claude-3-haiku-20240307', 'claude-2.1'],
    description: 'Anthropic Claude models'
  },
  google: {
    name: 'google',
    displayName: 'Google',
    models: ['gemini-pro', 'gemini-pro-vision'],
    description: 'Google Gemini models'
  },
  deepseek: {
    name: 'deepseek',
    displayName: 'Deepseek',
    models: ['deepseek-chat', 'deepseek-coder'],
    description: 'Deepseek AI models'
  }
};

export const SUPPORTED_PROVIDER_NAMES = [
  'openai',
  'anthropic',
  'google',
  'deepseek'
] as const;

export type SupportedProvider = typeof SUPPORTED_PROVIDER_NAMES[number];

// Default configurations
export const DEFAULT_PROVIDER_MODELS: Record<SupportedProvider, string> = {
  openai: 'gpt-4',
  anthropic: 'claude-3-sonnet-20240229',
  google: 'gemini-pro',
  deepseek: 'deepseek-chat'
};

export const DEFAULT_APP_CONFIG: AppConfig = {
  defaultProvider: 'openai',
  configDir: '~/.ai-cli-terminal',
  providers: {},
  tools: {
    enableShellTools: true,
    enableWebTools: true,
    enableFileTools: true,
    enableMemoryTools: true,
    requireConfirmation: true,
    sandboxMode: false,
    maxFileSize: '10MB',
    allowedCommands: ['ls', 'cat', 'grep', 'find', 'pwd', 'echo'],
    blockedCommands: ['rm', 'sudo', 'chmod', 'chown', 'dd', 'mkfs']
  },
  security: {
    requireToolConfirmation: true,
    sandboxMode: false,
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedPaths: [],
    blockedPaths: ['/etc', '/usr', '/bin', '/sbin', '/var'],
    allowedDomains: [],
    blockedDomains: []
  },
  ui: {
    theme: 'default',
    showTimestamps: true,
    showTokenCounts: true,
    maxHistorySize: 1000,
    autoSave: true,
    streamResponses: true
  },
  systemPrompts: {
    prompts: {},
    categories: ['General', 'Software Engineering', 'CLI Agent', 'Custom'],
    activePromptId: undefined,
    defaultPromptId: undefined
  },
  errorHandling: {
    retry: {
      maxAttempts: 3,
      baseDelay: 1000,
      backoffMultiplier: 2,
      maxDelay: 60000,
      enableJitter: true,
      jitterType: 'full',
      attemptTimeout: 30000
    },
    circuitBreaker: {
      failureThreshold: 5,
      recoveryTimeout: 30000,
      successThreshold: 2,
      monitoringWindow: 60000,
      enabled: true
    },
    enableErrorLogging: true,
    enableMetrics: true,
    showRetryProgress: true,
    allowRetryCancel: true,
    operationPolicies: {
      toolExecution: {
        maxAttempts: 2,
        baseDelay: 1000,
        backoffMultiplier: 2
      },
      providerCalls: {
        maxAttempts: 3,
        baseDelay: 1000,
        backoffMultiplier: 2,
        maxDelay: 30000
      },
      fileOperations: {
        maxAttempts: 2,
        baseDelay: 500,
        backoffMultiplier: 1.5
      },
      networkRequests: {
        maxAttempts: 3,
        baseDelay: 1000,
        backoffMultiplier: 2,
        maxDelay: 60000
      }
    }
  },
  preferences: {
    requireToolConfirmation: true
  }
};

// Error types
export class AICliError extends Error {
  public code: string;
  public details?: any;

  constructor(message: string, code: string = 'UNKNOWN_ERROR', details?: any) {
    super(message);
    this.name = 'AICliError';
    this.code = code;
    this.details = details;
  }
}

export class ProviderError extends AICliError {
  constructor(message: string, provider: string, details?: any) {
    super(message, 'PROVIDER_ERROR', { provider, ...details });
    this.name = 'ProviderError';
  }
}

export class ToolError extends AICliError {
  constructor(message: string, toolName: string, details?: any) {
    super(message, 'TOOL_ERROR', { toolName, ...details });
    this.name = 'ToolError';
  }
}

export class ConfigError extends AICliError {
  constructor(message: string, details?: any) {
    super(message, 'CONFIG_ERROR', details);
    this.name = 'ConfigError';
  }
}

// Event types for the system
export interface SystemEvent {
  type: string;
  timestamp: Date;
  data: any;
}

export interface ProviderEvent extends SystemEvent {
  type: 'provider.connected' | 'provider.disconnected' | 'provider.error';
  data: {
    provider: string;
    error?: string;
  };
}

export interface ToolEvent extends SystemEvent {
  type: 'tool.executed' | 'tool.failed' | 'tool.confirmed' | 'tool.denied';
  data: {
    toolName: string;
    parameters?: Record<string, any>;
    result?: ToolResult;
    error?: string;
  };
}

export interface ConversationEvent extends SystemEvent {
  type: 'conversation.started' | 'conversation.ended' | 'message.sent' | 'message.received';
  data: {
    conversationId: string;
    message?: ChatMessage;
  };
}

// Utility types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
